import pyautogui
import time
import os
from PIL import Image
import tkinter as tk
from tkinter import scrolledtext, messagebox
import keyboard


class RegionSelectorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Region Selector")
        self.root.geometry("600x500")

        # Initialize variables
        self.top_left = None
        self.bottom_right = None
        self.region = None
        self.screenshot_path = None

        # Create UI elements
        self.label_instruction = tk.Label(root, text="Hover and press Enter to set the region")
        self.label_instruction.pack(pady=10)

        self.label_result = tk.Label(root, text="Resulting Region: Not Set Yet", wraplength=550)
        self.label_result.pack(pady=10)

        self.button_start = tk.Button(root, text="Start Region Selection", command=self.start_selection)
        self.button_start.pack(pady=10)

        # Log display area
        self.log_frame = tk.LabelFrame(root, text="Log Information", padx=10, pady=10)
        self.log_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Text area for log display
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=8, width=70, wrap=tk.WORD)
        self.log_text.pack(fill="both", expand=True, pady=(0, 10))

        # Copy buttons frame
        self.copy_frame = tk.Frame(self.log_frame)
        self.copy_frame.pack(fill="x", pady=5)

        self.copy_topleft_btn = tk.Button(self.copy_frame, text="Copy Top-Left", command=self.copy_topleft, state="disabled")
        self.copy_topleft_btn.pack(side="left", padx=5)

        self.copy_bottomright_btn = tk.Button(self.copy_frame, text="Copy Bottom-Right", command=self.copy_bottomright, state="disabled")
        self.copy_bottomright_btn.pack(side="left", padx=5)

        self.copy_region_btn = tk.Button(self.copy_frame, text="Copy Region", command=self.copy_region, state="disabled")
        self.copy_region_btn.pack(side="left", padx=5)

        self.copy_path_btn = tk.Button(self.copy_frame, text="Copy Screenshot Path", command=self.copy_path, state="disabled")
        self.copy_path_btn.pack(side="left", padx=5)

        self.quit_button = tk.Button(root, text="Quit", command=root.quit)
        self.quit_button.pack(pady=5)

    def start_selection(self):
        self.label_instruction.config(text="Hover over the TOP-LEFT corner and press Enter...")
        self.root.update()

        keyboard.wait("enter")
        self.top_left = pyautogui.position()
        print(f"Top-left corner: {self.top_left}")

        self.label_instruction.config(text="Hover over the BOTTOM-RIGHT corner and press Enter...")
        self.root.update()

        keyboard.wait("enter")
        self.bottom_right = pyautogui.position()
        print(f"Bottom-right corner: {self.bottom_right}")

        # Calculate region
        x1, y1 = self.top_left
        x2, y2 = self.bottom_right
        self.region = (x1, y1, x2 - x1, y2 - y1)
        print(f"Region: {self.region}")

        # Update result on UI
        self.label_result.config(text=f"Resulting Region: {self.region}")

        # Save a screenshot of the region with the region coordinates in the filename
        save_folder = r"C:\Users\<USER>\Desktop\temp region dump"
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)  # Create folder if it doesn't exist

        filename = f"region_{x1}x{y1}_{x2-x1}x{y2-y1}.png"
        self.screenshot_path = os.path.join(save_folder, filename)
        pyautogui.screenshot(self.screenshot_path, region=self.region)
        print(f"Screenshot saved at: {self.screenshot_path}")

        # Update log display
        self.update_log_display()

        # Enable copy buttons
        self.copy_topleft_btn.config(state="normal")
        self.copy_bottomright_btn.config(state="normal")
        self.copy_region_btn.config(state="normal")
        self.copy_path_btn.config(state="normal")

        self.label_instruction.config(text="Region selection complete! Use copy buttons below.")

    def update_log_display(self):
        """Update the log display with current region information"""
        self.log_text.delete(1.0, tk.END)

        if self.top_left and self.bottom_right and self.region and self.screenshot_path:
            log_info = f"Top-left corner: Point(x={self.top_left.x}, y={self.top_left.y})\n"
            log_info += f"Bottom-right corner: Point(x={self.bottom_right.x}, y={self.bottom_right.y})\n"
            log_info += f"Region: {self.region}\n"
            log_info += f"Screenshot saved at: {self.screenshot_path}\n"

            self.log_text.insert(tk.END, log_info)

    def copy_topleft(self):
        """Copy top-left corner information to clipboard"""
        if self.top_left:
            text = f"Top-left corner: Point(x={self.top_left.x}, y={self.top_left.y})"
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Copied", "Top-left corner information copied to clipboard!")

    def copy_bottomright(self):
        """Copy bottom-right corner information to clipboard"""
        if self.bottom_right:
            text = f"Bottom-right corner: Point(x={self.bottom_right.x}, y={self.bottom_right.y})"
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Copied", "Bottom-right corner information copied to clipboard!")

    def copy_region(self):
        """Copy region information to clipboard"""
        if self.region:
            text = f"Region: {self.region}"
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Copied", "Region information copied to clipboard!")

    def copy_path(self):
        """Copy screenshot path to clipboard"""
        if self.screenshot_path:
            text = f"Screenshot saved at: {self.screenshot_path}"
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Copied", "Screenshot path copied to clipboard!")


if __name__ == "__main__":
    root = tk.Tk()
    app = RegionSelectorApp(root)
    root.mainloop()
