<!DOCTYPE html>
<html lang="en">
    <head>
        <title>VNC playback</title>
        <script type="module" src="./playback-ui.js"></script>
    </head>
    <body>

        Iterations: <input id='iterations'>&nbsp;
        Perftest:<input type='radio' id='mode1' name='mode' checked>&nbsp;
        Realtime:<input type='radio' id='mode2' name='mode'>&nbsp;&nbsp;

        <input id='startButton' type='button' value='Start' disabled>&nbsp;

        <br><br>

        Results:<br>
        <textarea id="messages" cols=80 rows=25></textarea>

        <br><br>

        <div id="VNC_screen">
            <div id="VNC_status">Loading</div>
        </div>
    </body>
</html>
